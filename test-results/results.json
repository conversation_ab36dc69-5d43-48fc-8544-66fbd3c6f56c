{"numTotalTestSuites": 10, "numPassedTestSuites": 5, "numFailedTestSuites": 5, "numPendingTestSuites": 0, "numTotalTests": 31, "numPassedTests": 27, "numFailedTests": 4, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752845676653, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Referral System", "Clipboard Functionality"], "fullName": "Referral System Clipboard Functionality should copy text using modern clipboard API when available", "status": "passed", "title": "should copy text using modern clipboard API when available", "duration": 8.631965000000037, "failureMessages": [], "location": {"line": 77, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Clipboard Functionality"], "fullName": "Referral System Clipboard Functionality should fallback to execCommand when clipboard API fails", "status": "passed", "title": "should fallback to execCommand when clipboard API fails", "duration": 3.2842640000003485, "failureMessages": [], "location": {"line": 94, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Clipboard Functionality"], "fullName": "Referral System Clipboard Functionality should return false when all clipboard methods fail", "status": "passed", "title": "should return false when all clipboard methods fail", "duration": 2.131995000000188, "failureMessages": [], "location": {"line": 112, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Clipboard Functionality"], "fullName": "Referral System Clipboard Functionality should show success toast when copyToClipboardWithFeedback succeeds", "status": "passed", "title": "should show success toast when copyToClipboardWithFeedback succeeds", "duration": 1.7028409999998075, "failureMessages": [], "location": {"line": 127, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Clipboard Functionality"], "fullName": "Referral System Clipboard Functionality should show error toast when copyToClipboardWithFeedback fails", "status": "failed", "title": "should show error toast when copyToClipboardWithFeedback fails", "duration": 30.79311100000041, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ StringContaining \"Failed!\" ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[32m-   StringContaining \"Failed!\",\u001b[90m\n\u001b[31m+   \"Failed!. Please manually copy: test text\",\u001b[90m\n\u001b[31m+   {\u001b[90m\n\u001b[31m+     \"duration\": 8000,\u001b[90m\n\u001b[31m+     \"style\": {\u001b[90m\n\u001b[31m+       \"maxWidth\": \"500px\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[31m+   },\u001b[90m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/vitest/node_modules/chai/chai.js:1706:25)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:153:27\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ StringContaining \"Failed!\" ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[32m-   StringContaining \"Failed!\",\u001b[90m\n\u001b[31m+   \"Failed!. Please manually copy: test text\",\u001b[90m\n\u001b[31m+   {\u001b[90m\n\u001b[31m+     \"duration\": 8000,\u001b[90m\n\u001b[31m+     \"style\": {\u001b[90m\n\u001b[31m+       \"maxWidth\": \"500px\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[31m+   },\u001b[90m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/vitest/node_modules/chai/chai.js:1706:25)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:153:27\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ StringContaining \"Failed!\" ]\u001b[90m\n\nReceived: \n\n\u001b[1m  1st spy call:\n\n\u001b[22m\u001b[2m  [\u001b[22m\n\u001b[32m-   StringContaining \"Failed!\",\u001b[90m\n\u001b[31m+   \"Failed!. Please manually copy: test text\",\u001b[90m\n\u001b[31m+   {\u001b[90m\n\u001b[31m+     \"duration\": 8000,\u001b[90m\n\u001b[31m+     \"style\": {\u001b[90m\n\u001b[31m+       \"maxWidth\": \"500px\",\u001b[90m\n\u001b[31m+     },\u001b[90m\n\u001b[31m+   },\u001b[90m\n\u001b[2m  ]\u001b[22m\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m1\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/vitest/node_modules/chai/chai.js:1706:25)\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:153:27\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Generation"], "fullName": "Referral System Referral Link Generation should generate valid referral link for valid address", "status": "passed", "title": "should generate valid referral link for valid address", "duration": 2.764991000000009, "failureMessages": [], "location": {"line": 160, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Generation"], "fullName": "Referral System Referral Link Generation should return null for invalid address", "status": "passed", "title": "should return null for invalid address", "duration": 3.324709999999868, "failureMessages": [], "location": {"line": 169, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Generation"], "fullName": "Referral System Referral Link Generation should return null for empty address", "status": "passed", "title": "should return null for empty address", "duration": 1.315454999999929, "failureMessages": [], "location": {"line": 175, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Generation"], "fullName": "Referral System Referral Link Generation should return null for non-string input", "status": "passed", "title": "should return null for non-string input", "duration": 0.9378269999997428, "failureMessages": [], "location": {"line": 181, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "URL Parameter Extraction"], "fullName": "Referral System URL Parameter Extraction should extract valid referrer from URL", "status": "passed", "title": "should extract valid referrer from URL", "duration": 2.4655500000003485, "failureMessages": [], "location": {"line": 189, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "URL Parameter Extraction"], "fullName": "Referral System URL Parameter Extraction should return null for invalid referrer in URL", "status": "passed", "title": "should return null for invalid referrer in URL", "duration": 1.0279359999999542, "failureMessages": [], "location": {"line": 202, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "URL Parameter Extraction"], "fullName": "Referral System URL Parameter Extraction should return null when no referrer parameter in URL", "status": "passed", "title": "should return null when no referrer parameter in URL", "duration": 1.3396609999999782, "failureMessages": [], "location": {"line": 215, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referrer Validation"], "fullName": "Referral System Referrer Validation should validate correct referrer address", "status": "passed", "title": "should validate correct referrer address", "duration": 1.3937920000003032, "failureMessages": [], "location": {"line": 230, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referrer Validation"], "fullName": "Referral System Referrer Validation should reject invalid referrer address", "status": "passed", "title": "should reject invalid referrer address", "duration": 0.9896640000001753, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referrer Validation"], "fullName": "Referral System Referrer Validation should reject self-referral", "status": "passed", "title": "should reject self-referral", "duration": 1.8910729999997784, "failureMessages": [], "location": {"line": 242, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referrer Validation"], "fullName": "Referral System Referrer Validation should allow different addresses", "status": "passed", "title": "should allow different addresses", "duration": 0.9097750000000815, "failureMessages": [], "location": {"line": 248, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should validate correct referral link", "status": "passed", "title": "should validate correct referral link", "duration": 4.138675000000148, "failureMessages": [], "location": {"line": 262, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should reject referral link with invalid address", "status": "passed", "title": "should reject referral link with invalid address", "duration": 9.947590000000218, "failureMessages": [], "location": {"line": 268, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should reject referral link with mismatched URL and address", "status": "passed", "title": "should reject referral link with mismatched URL and address", "duration": 1.722889000000123, "failureMessages": [], "location": {"line": 279, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should reject referral link with invalid code format", "status": "passed", "title": "should reject referral link with invalid code format", "duration": 1.5055320000001302, "failureMessages": [], "location": {"line": 290, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should reject null or undefined referral link", "status": "passed", "title": "should reject null or undefined referral link", "duration": 1.5906409999997777, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Referral Link Validation"], "fullName": "Referral System Referral Link Validation should reject referral link with invalid URL", "status": "passed", "title": "should reject referral link with invalid URL", "duration": 1.8924659999997857, "failureMessages": [], "location": {"line": 306, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Copy Functions with Validation"], "fullName": "Referral System Copy Functions with Validation should copy valid referral link", "status": "passed", "title": "should copy valid referral link", "duration": 2.9963450000000194, "failureMessages": [], "location": {"line": 338, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Copy Functions with Validation"], "fullName": "Referral System Copy Functions with Validation should reject invalid referral link", "status": "passed", "title": "should reject invalid referral link", "duration": 1.7386780000001636, "failureMessages": [], "location": {"line": 345, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Copy Functions with Validation"], "fullName": "Referral System Copy Functions with Validation should copy valid referral code", "status": "passed", "title": "should copy valid referral code", "duration": 1.4332460000000538, "failureMessages": [], "location": {"line": 357, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Copy Functions with Validation"], "fullName": "Referral System Copy Functions with Validation should reject invalid referral code", "status": "passed", "title": "should reject invalid referral code", "duration": 1.2334810000002108, "failureMessages": [], "location": {"line": 364, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Share Function with Validation"], "fullName": "Referral System Share Function with Validation should reject invalid referral link for sharing", "status": "passed", "title": "should reject invalid referral link for sharing", "duration": 1.5995769999999538, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "Share Function with Validation"], "fullName": "Referral System Share Function with Validation should handle Web Share API cancellation", "status": "failed", "title": "should handle Web Share API cancellation", "duration": 18.201575999999932, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:404:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:404:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:404:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 391, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "<PERSON> Cases and <PERSON><PERSON><PERSON>ling"], "fullName": "Referral System Edge Cases and Error Handling should handle window.location being undefined", "status": "passed", "title": "should handle window.location being undefined", "duration": 3.296997999999803, "failureMessages": [], "location": {"line": 410, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "<PERSON> Cases and <PERSON><PERSON><PERSON>ling"], "fullName": "Referral System Edge Cases and Error Handling should handle document being undefined", "status": "failed", "title": "should handle document being undefined", "duration": 6.937991000000238, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:425:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "TypeError: Cannot delete property 'document' of #<Object>\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:421:7\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runSuite (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "TypeError: Cannot delete property 'document' of #<Object>\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:421:7\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runSuite (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 419, "column": 5}, "meta": {}}, {"ancestorTitles": ["Referral System", "<PERSON> Cases and <PERSON><PERSON><PERSON>ling"], "fullName": "Referral System Edge Cases and Error Handling should handle navigator being undefined", "status": "failed", "title": "should handle navigator being undefined", "duration": 10.122998999999709, "failureMessages": ["AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:436:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:436:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected true to be false // Object.is equality\n    at /home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts:436:22\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 430, "column": 5}, "meta": {}}], "startTime": 1752845681406, "endTime": 1752845681543.123, "status": "failed", "message": "", "name": "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/lib/__tests__/referral.test.ts"}]}