{"numTotalTestSuites": 3, "numPassedTestSuites": 3, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 6, "numPassedTests": 6, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752842459066, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Gmail Authentication Integration"], "fullName": "Gmail Authentication Integration should have Gmail authentication enabled in AppKit configuration", "status": "passed", "title": "should have Gmail authentication enabled in AppKit configuration", "duration": 6.350306999999702, "failureMessages": [], "location": {"line": 72, "column": 3}, "meta": {}}, {"ancestorTitles": ["Gmail Authentication Integration"], "fullName": "Gmail Authentication Integration should open wallet modal when connect wallet is called", "status": "passed", "title": "should open wallet modal when connect wallet is called", "duration": 116.79671300000064, "failureMessages": [], "location": {"line": 78, "column": 3}, "meta": {}}, {"ancestorTitles": ["Gmail Authentication Integration"], "fullName": "Gmail Authentication Integration should handle wallet connection state changes", "status": "passed", "title": "should handle wallet connection state changes", "duration": 27.924418999999943, "failureMessages": [], "location": {"line": 105, "column": 3}, "meta": {}}, {"ancestorTitles": ["Gmail Authentication Integration"], "fullName": "Gmail Authentication Integration should provide Web3 context with connection methods", "status": "passed", "title": "should provide Web3 context with connection methods", "duration": 19.64115099999981, "failureMessages": [], "location": {"line": 124, "column": 3}, "meta": {}}, {"ancestorTitles": ["AppKit Configuration"], "fullName": "AppKit Configuration should have correct feature flags for Gmail authentication", "status": "passed", "title": "should have correct feature flags for Gmail authentication", "duration": 1.9090660000001662, "failureMessages": [], "location": {"line": 145, "column": 3}, "meta": {}}, {"ancestorTitles": ["AppKit Configuration"], "fullName": "AppKit Configuration should have valid contract addresses", "status": "passed", "title": "should have valid contract addresses", "duration": 1.574337000000014, "failureMessages": [], "location": {"line": 152, "column": 3}, "meta": {}}], "startTime": 1752842465047, "endTime": 1752842465223.5742, "status": "passed", "message": "", "name": "/home/<USER>/Documents/GKM/BlockCoopCpanel/frontend/src/tests/gmail-authentication.test.tsx"}]}